[2025-08-14 00:04:12] production.INFO: User offline {"user_id":14} 
[2025-08-14 00:12:35] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"storyboard","mapped_task_type":"text_generation_storyboard","user_id":14,"session_id":"ws_nJAUqAAa5rorjSWufgX0cZUZ5lPvxsUW"} 
[2025-08-14 00:12:35] production.INFO: WebSocket连接认证成功 {"session_id":"ws_nJAUqAAa5rorjSWufgX0cZUZ5lPvxsUW","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1"} 
[2025-08-14 00:17:14] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-14 00:17:14] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-14 00:17:14] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-14 00:17:14] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-14 00:17:14] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-14 00:23:12] production.INFO: 🔍 WebSocket服务器 - 开始启动Redis订阅协程  
[2025-08-14 00:23:12] production.INFO: 🔍 WebSocket服务器 - 使用Laravel Redis门面  
[2025-08-14 00:23:12] production.INFO: ✅ WebSocket服务器 - Redis连接成功，启动订阅协程  
[2025-08-14 00:23:12] production.INFO: 🔍 WebSocket服务器 - 开始使用Redis列表轮询消息  
[2025-08-14 00:23:12] production.INFO: ✅ WebSocket服务器 - Redis列表轮询已启动  
[2025-08-14 00:27:12] production.INFO: WebSocket业务类型映射 {"frontend_business_type":"storyboard","mapped_task_type":"text_generation_storyboard","user_id":14,"session_id":"ws_ekou3v0o5enk43CF5G2TUPcFOs6dXqSR"} 
[2025-08-14 00:27:12] production.INFO: WebSocket连接认证成功 {"session_id":"ws_ekou3v0o5enk43CF5G2TUPcFOs6dXqSR","user_id":14,"client_type":"python_tool","connection_ip":"127.0.0.1"} 
[2025-08-14 00:29:48] production.INFO: 积分检查 {"user_id":14,"required_amount":1.4105,"current_balance":"79.22","sufficient":true,"business_type":"text_generation","business_id":null} 
[2025-08-14 00:29:48] production.INFO: 积分冻结成功 {"user_id":14,"amount":1.4105,"business_type":"text_generation","business_id":null,"transaction_id":45,"freeze_id":45} 
[2025-08-14 00:29:48] production.INFO: WebSocket文本生成 - 参数传递检查 {"task_id":"text_gen_1755102588_64vLCZW8","user_id":14,"estimated_cost":1.4105,"generation_params":{"max_tokens":"1000","temperature":0.7,"top_p":0.9,"estimated_cost":1.4105},"generation_params_keys":["max_tokens","temperature","top_p","estimated_cost"],"generation_params_count":4} 
[2025-08-14 00:30:02] production.INFO: 积分检查 {"user_id":14,"required_amount":1.4105,"current_balance":"77.81","sufficient":true,"business_type":"text_generation","business_id":null} 
[2025-08-14 00:30:02] production.INFO: 积分冻结成功 {"user_id":14,"amount":1.4105,"business_type":"text_generation","business_id":null,"transaction_id":46,"freeze_id":46} 
[2025-08-14 00:30:02] production.INFO: WebSocket文本生成 - 参数传递检查 {"task_id":"text_gen_1755102602_QUl82rED","user_id":14,"estimated_cost":1.4105,"generation_params":{"max_tokens":"1000","temperature":0.7,"top_p":0.9,"estimated_cost":1.4105},"generation_params_keys":["max_tokens","temperature","top_p","estimated_cost"],"generation_params_count":4} 
[2025-08-14 00:34:11] production.INFO: WebSocket推送进度 - 任务开始 {"task_id":"text_gen_1755102588_64vLCZW8","user_id":14,"progress":10,"message":"开始文本生成任务","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755102851,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-14 00:34:11] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755102588_64vLCZW8","user_id":14} 
[2025-08-14 00:34:11] production.INFO: WebSocket推送结果 - 任务开始 {"task_id":"text_gen_1755102588_64vLCZW8","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-14 00:34:11] production.INFO: WebSocket推送进度 - 连接AI平台 {"task_id":"text_gen_1755102588_64vLCZW8","user_id":14,"progress":30,"message":"连接AI平台","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755102851,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-14 00:34:11] production.WARNING: 未找到任务记录 {"task_id":"text_gen_1755102588_64vLCZW8","user_id":14} 
[2025-08-14 00:34:11] production.INFO: WebSocket推送结果 - 连接AI平台 {"task_id":"text_gen_1755102588_64vLCZW8","user_id":14,"push_success":"no","push_method":"Redis通道桥接"} 
[2025-08-14 00:34:11] production.INFO: 从WebSocket会话获取business_type {"task_id":"text_gen_1755102588_64vLCZW8","user_id":14,"session_id":"ws_ekou3v0o5enk43CF5G2TUPcFOs6dXqSR","business_type":"text_generation_storyboard"} 
[2025-08-14 00:34:11] production.INFO: ProcessTextGeneration Job - 参数检查 {"task_id":"text_gen_1755102588_64vLCZW8","user_id":14,"generation_params":{"max_tokens":"1000","temperature":0.7,"top_p":0.9,"estimated_cost":1.4105},"generation_params_keys":["max_tokens","temperature","top_p","estimated_cost"],"generation_params_count":4,"has_estimated_cost":true,"estimated_cost_value":1.4105} 
[2025-08-14 00:34:11] production.DEBUG: Lumen事务开始 {"context":"AiGenerationService.generateText","level":1,"action":"real_begin","framework":"Lumen 10"} 
[2025-08-14 00:34:11] production.INFO: generateText方法 - 参数检查 {"user_id":14,"external_task_id":"text_gen_1755102588_64vLCZW8","generation_params":{"max_tokens":"1000","temperature":0.7,"top_p":0.9,"estimated_cost":1.4105},"generation_params_keys":["max_tokens","temperature","top_p","estimated_cost"],"generation_params_count":4,"has_estimated_cost":true,"estimated_cost_value":1.4105} 
[2025-08-14 00:34:11] production.INFO: 🔍 开始执行文本生成任务 {"task_id":45,"user_id":14,"platform":"deepseek","model_name":"deepseek-chat","task_type":"text_generation_storyboard","external_task_id":"text_gen_1755102588_64vLCZW8"} 
[2025-08-14 00:34:11] production.INFO: 🔍 准备调用AI服务 {"task_id":45,"platform":"deepseek","task_type":"text_generation_storyboard","request_data":{"model":"deepseek-chat","max_tokens":"1000","temperature":0.7,"top_p":0.9,"prompt_length":0},"ai_service_url":"https://aiapi.tiptop.cn/"} 
[2025-08-14 00:34:11] production.INFO: AI服务调用 {"platform":"deepseek","mode":"mock","data_size":117} 
[2025-08-14 00:34:11] production.INFO: 调用模拟服务 {"platform":"deepseek","url":"https://aiapi.tiptop.cn/deepseek/chat/completions","timeout":"30"} 
[2025-08-14 00:34:11] production.INFO: 模拟服务调用成功 {"platform":"deepseek","status":200,"response_size":802} 
[2025-08-14 00:34:11] production.INFO: 用户偏好记录更新成功 {"user_id":14,"platform":"deepseek","task_type":"text_generation_storyboard","usage_count":7,"success_rate":1.0} 
[2025-08-14 00:34:11] production.INFO: 🔍 AI服务调用完成 {"task_id":45,"platform":"deepseek","response_success":true,"response_status":"unknown","response_mode":"mock","has_data":true,"has_error":false} 
[2025-08-14 00:34:11] production.INFO: 🔍 AI服务调用成功，开始解析响应 {"task_id":45,"response_data_keys":["id","object","created","model","choices","usage"],"response_data_type":"array"} 
[2025-08-14 00:34:11] production.INFO: 🔍 AI响应解析完成 {"task_id":45,"generated_text_length":349,"tokens_used":105,"finish_reason":"stop","model":"deepseek-chat"} 
[2025-08-14 00:34:11] production.INFO: 🔍 开始完成任务并消费积分 {"task_id":45,"output_data_keys":["text","finish_reason","model","usage"],"tokens_used":105} 
[2025-08-14 00:34:11] production.INFO: 积分消费成功 {"transaction_id":45,"user_id":14,"amount":"1.41"} 
[2025-08-14 00:34:11] production.INFO: 🔍 任务完成，积分已消费 {"task_id":45,"task_status":"completed","processing_time_ms":451} 
[2025-08-14 00:34:11] production.WARNING: text_generation_storyboard类型任务缺少项目ID或样式参数 {"user_id":14,"project_id":3,"aspect_ratio":"","style_id":"","task_type":"text_generation_storyboard"} 
[2025-08-14 00:34:11] production.INFO: 文本生成任务创建成功 {"task_id":45,"user_id":14,"model_id":1,"estimated_cost":1.4105} 
[2025-08-14 00:34:11] production.DEBUG: 事务提交 {"context":"AiGenerationService.generateText","level":1,"action":"real_commit"} 
[2025-08-14 00:34:11] production.INFO: WebSocket推送进度 - 保存文本数据 {"task_id":"text_gen_1755102588_64vLCZW8","user_id":14,"progress":80,"message":"保存文本数据","push_type":"ai_generation_progress","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755102851,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-14 00:34:11] production.INFO: WebSocket推送开始 {"push_id":"push_689cbe8374bd5","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755102588_64vLCZW8","data_size":237,"max_attempts":3,"timestamp":"2025-08-14T00:34:11+08:00"} 
[2025-08-14 00:34:11] production.INFO: Redis列表消息推送成功 {"list_key":"websocket:queue:ws_ekou3v0o5enk43CF5G2TUPcFOs6dXqSR","session_id":"ws_ekou3v0o5enk43CF5G2TUPcFOs6dXqSR","user_id":14,"business_type":"text_generation_storyboard","message_type":"ai_generation_progress","list_length":1,"data_size":514} 
[2025-08-14 00:34:11] production.INFO: WebSocket消息推送成功 {"session_id":"ws_ekou3v0o5enk43CF5G2TUPcFOs6dXqSR","event_type":"ai_generation_progress","data_size":237} 
[2025-08-14 00:34:11] production.INFO: WebSocket推送成功 {"push_id":"push_689cbe8374bd5","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755102588_64vLCZW8","attempt":1,"success_count":1,"attempt_duration_ms":34.2,"total_duration_ms":40.3,"timestamp":"2025-08-14T00:34:11+08:00"} 
[2025-08-14 00:34:11] production.INFO: AI生成进度推送成功 {"task_id":"text_gen_1755102588_64vLCZW8","user_id":14,"progress":80,"message":"保存文本数据"} 
[2025-08-14 00:34:11] production.INFO: WebSocket推送结果 - 保存文本数据 {"task_id":"text_gen_1755102588_64vLCZW8","user_id":14,"push_success":"yes","push_method":"Redis通道桥接"} 
[2025-08-14 00:34:11] production.INFO: WebSocket推送进度 - 文本生成完成 {"task_id":"text_gen_1755102588_64vLCZW8","user_id":14,"progress":100,"message":"文本生成完成","push_type":"ai_generation_progress"} 
[2025-08-14 00:34:11] production.INFO: WebSocket推送开始 {"push_id":"push_689cbe837f6de","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755102588_64vLCZW8","data_size":238,"max_attempts":3,"timestamp":"2025-08-14T00:34:11+08:00"} 
[2025-08-14 00:34:11] production.INFO: 🔍 WebSocket服务器 - 从Redis列表获取消息 {"session_id":"ws_ekou3v0o5enk43CF5G2TUPcFOs6dXqSR","list_key":"websocket:queue:ws_ekou3v0o5enk43CF5G2TUPcFOs6dXqSR","message_length":514} 
[2025-08-14 00:34:11] production.INFO: ✅ WebSocket服务器 - 准备推送消息到WebSocket连接 {"session_id":"ws_ekou3v0o5enk43CF5G2TUPcFOs6dXqSR","message_type":"ai_generation_progress"} 
[2025-08-14 00:34:11] production.INFO: Redis列表消息推送成功 {"list_key":"websocket:queue:ws_ekou3v0o5enk43CF5G2TUPcFOs6dXqSR","session_id":"ws_ekou3v0o5enk43CF5G2TUPcFOs6dXqSR","user_id":14,"business_type":"text_generation_storyboard","message_type":"ai_generation_progress","list_length":1,"data_size":515} 
[2025-08-14 00:34:11] production.INFO: WebSocket消息推送成功 {"session_id":"ws_ekou3v0o5enk43CF5G2TUPcFOs6dXqSR","event_type":"ai_generation_progress","data_size":238} 
[2025-08-14 00:34:11] production.INFO: WebSocket消息推送成功 {"session_id":"ws_ekou3v0o5enk43CF5G2TUPcFOs6dXqSR","connection_id":"1","event":"ai_generation_progress"} 
[2025-08-14 00:34:11] production.INFO: WebSocket推送成功 {"push_id":"push_689cbe837f6de","user_id":14,"event_type":"ai_generation_progress","context":"ai_generation_progress:text_gen_1755102588_64vLCZW8","attempt":1,"success_count":1,"attempt_duration_ms":8.89,"total_duration_ms":15.07,"timestamp":"2025-08-14T00:34:11+08:00"} 
[2025-08-14 00:34:11] production.INFO: AI生成进度推送成功 {"task_id":"text_gen_1755102588_64vLCZW8","user_id":14,"progress":100,"message":"文本生成完成"} 
[2025-08-14 00:34:11] production.INFO: WebSocket推送事件 - 任务完成 {"task_id":"text_gen_1755102588_64vLCZW8","user_id":14,"push_type":"ai_generation_completed","redis_channel":"websocket:push:session_*","push_method":"Redis通道桥接","timestamp":1755102851,"job_class":"App\\Jobs\\ProcessTextGeneration","job_method":"handle"} 
[2025-08-14 00:34:11] production.INFO: WebSocket推送开始 {"push_id":"push_689cbe8383ea3","user_id":14,"event_type":"ai_generation_completed","context":"ai_generation_completed:text_gen_1755102588_64vLCZW8","data_size":1128,"max_attempts":3,"timestamp":"2025-08-14T00:34:11+08:00"} 
[2025-08-14 00:34:11] production.INFO: Redis列表消息推送成功 {"list_key":"websocket:queue:ws_ekou3v0o5enk43CF5G2TUPcFOs6dXqSR","session_id":"ws_ekou3v0o5enk43CF5G2TUPcFOs6dXqSR","user_id":14,"business_type":"text_generation_storyboard","message_type":"ai_generation_completed","list_length":2,"data_size":1406} 
[2025-08-14 00:34:11] production.INFO: WebSocket消息推送成功 {"session_id":"ws_ekou3v0o5enk43CF5G2TUPcFOs6dXqSR","event_type":"ai_generation_completed","data_size":1128} 
[2025-08-14 00:34:11] production.INFO: WebSocket推送成功 {"push_id":"push_689cbe8383ea3","user_id":14,"event_type":"ai_generation_completed","context":"ai_generation_completed:text_gen_1755102588_64vLCZW8","attempt":1,"success_count":1,"attempt_duration_ms":8.17,"total_duration_ms":15.26,"timestamp":"2025-08-14T00:34:11+08:00"} 
[2025-08-14 00:34:11] production.INFO: AI生成完成推送成功 {"task_id":"text_gen_1755102588_64vLCZW8","user_id":14,"task_type":"text_generation_storyboard"} 
[2025-08-14 00:34:11] production.INFO: WebSocket推送结果 - 任务完成 {"task_id":"text_gen_1755102588_64vLCZW8","user_id":14,"push_success":"yes","push_method":"Redis通道桥接"} 
[2025-08-14 00:34:11] production.INFO: 文本生成任务完成 {"task_id":"text_gen_1755102588_64vLCZW8","user_id":14,"context":"分镜大师","ai_task_id":45,"cost":0} 
[2025-08-14 00:34:11] production.INFO: 🔍 WebSocket服务器 - 从Redis列表获取消息 {"session_id":"ws_ekou3v0o5enk43CF5G2TUPcFOs6dXqSR","list_key":"websocket:queue:ws_ekou3v0o5enk43CF5G2TUPcFOs6dXqSR","message_length":515} 
[2025-08-14 00:34:11] production.INFO: ✅ WebSocket服务器 - 准备推送消息到WebSocket连接 {"session_id":"ws_ekou3v0o5enk43CF5G2TUPcFOs6dXqSR","message_type":"ai_generation_progress"} 
[2025-08-14 00:34:11] production.INFO: WebSocket消息推送成功 {"session_id":"ws_ekou3v0o5enk43CF5G2TUPcFOs6dXqSR","connection_id":"1","event":"ai_generation_progress"} 
[2025-08-14 00:34:11] production.INFO: 🔍 WebSocket服务器 - 从Redis列表获取消息 {"session_id":"ws_ekou3v0o5enk43CF5G2TUPcFOs6dXqSR","list_key":"websocket:queue:ws_ekou3v0o5enk43CF5G2TUPcFOs6dXqSR","message_length":1406} 
[2025-08-14 00:34:11] production.INFO: ✅ WebSocket服务器 - 准备推送消息到WebSocket连接 {"session_id":"ws_ekou3v0o5enk43CF5G2TUPcFOs6dXqSR","message_type":"ai_generation_completed"} 
[2025-08-14 00:34:11] production.INFO: WebSocket消息推送成功 {"session_id":"ws_ekou3v0o5enk43CF5G2TUPcFOs6dXqSR","connection_id":"1","event":"ai_generation_completed"} 
